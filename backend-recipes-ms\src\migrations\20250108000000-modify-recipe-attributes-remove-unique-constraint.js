'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('🔄 Starting RecipeAttributes table modification...');

      // Step 1: Drop the existing unique constraint
      console.log('📋 Dropping unique constraint...');
      try {
        await queryInterface.removeIndex('mo_recipe_attributes', 'unique_recipe_attributes_composite', { transaction });
        console.log('✅ Unique constraint dropped successfully');
      } catch (error) {
        console.log('⚠️  Unique constraint may not exist, continuing...');
      }

      // Step 2: Drop existing primary key constraint
      console.log('📋 Dropping existing primary key constraint...');
      try {
        await queryInterface.sequelize.query('ALTER TABLE `mo_recipe_attributes` DROP PRIMARY KEY', { transaction });
        console.log('✅ Primary key constraint dropped');
      } catch (error) {
        console.log('⚠️  Primary key constraint may not exist, continuing...');
      }

      // Step 3: Add auto-increment ID column as new primary key
      console.log('📋 Adding auto-increment ID column...');
      await queryInterface.addColumn('mo_recipe_attributes', 'id', {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
        first: true // Add as first column
      }, { transaction });
      console.log('✅ Auto-increment ID column added');

      // Step 4: Ensure foreign key constraints are properly set
      console.log('📋 Ensuring foreign key constraints...');
      try {
        // Check if foreign key constraints exist and recreate if needed
        await queryInterface.addConstraint('mo_recipe_attributes', {
          fields: ['recipe_id'],
          type: 'foreign key',
          name: 'fk_recipe_attributes_recipe_id',
          references: {
            table: 'mo_recipe',
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
          transaction
        });
      } catch (error) {
        console.log('⚠️  Recipe foreign key constraint may already exist, continuing...');
      }

      try {
        await queryInterface.addConstraint('mo_recipe_attributes', {
          fields: ['attributes_id'],
          type: 'foreign key',
          name: 'fk_recipe_attributes_attributes_id',
          references: {
            table: 'mo_food_attributes',
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
          transaction
        });
      } catch (error) {
        console.log('⚠️  Attributes foreign key constraint may already exist, continuing...');
      }

      // Step 5: Add new non-unique index for performance
      console.log('📋 Adding performance index...');
      await queryInterface.addIndex('mo_recipe_attributes', ['recipe_id', 'attributes_id'], {
        name: 'idx_recipe_attributes_composite',
        transaction
      });
      console.log('✅ Performance index added');

      await transaction.commit();
      console.log('✅ RecipeAttributes table modification completed successfully!');
      console.log('🎉 HACCP attributes now support unlimited duplicate entries per attribute ID');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error modifying RecipeAttributes table:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('🔄 Reverting RecipeAttributes table modification...');

      // Step 1: Remove the performance index
      try {
        await queryInterface.removeIndex('mo_recipe_attributes', 'idx_recipe_attributes_composite', { transaction });
      } catch (error) {
        console.log('⚠️  Index may not exist, continuing...');
      }

      // Step 2: Remove foreign key constraints
      try {
        await queryInterface.removeConstraint('mo_recipe_attributes', 'fk_recipe_attributes_recipe_id', { transaction });
        await queryInterface.removeConstraint('mo_recipe_attributes', 'fk_recipe_attributes_attributes_id', { transaction });
      } catch (error) {
        console.log('⚠️  Some constraints may not exist, continuing...');
      }

      // Step 3: Remove the auto-increment ID column
      await queryInterface.removeColumn('mo_recipe_attributes', 'id', { transaction });

      // Step 4: Restore original primary key and constraints
      // Note: This is a simplified rollback - in production, you might need to handle data migration
      console.log('⚠️  Manual intervention may be required to fully restore original schema');

      await transaction.commit();
      console.log('✅ RecipeAttributes table rollback completed');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error rolling back RecipeAttributes table:', error);
      throw error;
    }
  }
};
